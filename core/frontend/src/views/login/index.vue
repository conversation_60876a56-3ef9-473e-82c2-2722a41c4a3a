<template>
  <div>
    <div
      v-show="contentShow"
      class="login-page"
    >
      <!-- 左侧装饰区域 -->
      <div class="login-decoration-section">
        <div class="brand-logo">
          <span class="brand-text">FusionAI 数据洞察</span>
        </div>
        <div class="decoration-container">
          <div class="decoration-image decoration-1" />
          <div class="decoration-image decoration-2" />
        </div>
      </div>

      <!-- 右侧登录区域 -->
      <div class="login-form-section">
        <div class="login-container">
          <div
            v-loading="loading"
            class="login-content"
          >
            <!-- 二维码切换按钮 -->
            <div
              v-show="qrTypes.length"
              :class="codeShow ? 'trans-pc' : 'trans'"
              @click="showQr"
            >
              <div
                v-show="imgAppShow"
                class="imgApp"
              />
            </div>

            <!-- 登录表单 -->
            <el-form
              v-show="!codeShow"
              ref="loginForm"
              :model="loginForm"
              :rules="loginRules"
              size="default"
              class="login-form"
            >
              <div class="form-title">
                <h2>登录</h2>
                <p class="form-subtitle">数据洞察</p>
              </div>

              <div class="form-content">
                <el-form-item v-if="radioTypes.length > 1">
                  <el-radio-group
                    v-if="radioTypes.length > 1"
                    v-model="loginForm.loginType"
                    @change="changeLoginType"
                  >
                    <el-radio
                      :label="0"
                      size="mini"
                    >{{ $t('login.default_login') }}</el-radio>
                    <el-radio
                      v-if="loginTypes.includes(1)"
                      :label="1"
                      size="mini"
                    >LDAP</el-radio>
                    <el-radio
                      v-if="loginTypes.includes(2)"
                      :label="2"
                      size="mini"
                    >OIDC</el-radio>
                    <el-radio
                      v-if="loginTypes.includes(7)"
                      :label="7"
                      size="mini"
                    >Lark</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item prop="username">
                  <label class="form-label">ID</label>
                  <el-input
                    v-model="loginForm.username"
                    placeholder="请输入ID"
                    autofocus
                    :disabled="loginTypes.includes(2) && loginForm.loginType === 2"
                  />
                </el-form-item>

                <el-form-item prop="password">
                  <label class="form-label">密码</label>
                  <el-input
                    v-model="loginForm.password"
                    placeholder="请输入密码"
                    show-password
                    maxlength="30"
                    show-word-limit
                    autocomplete="new-password"
                    :disabled="loginTypes.includes(2) && loginForm.loginType === 2"
                    @keypress.enter.native="handleLogin"
                  />
                </el-form-item>
              </div>

              <div class="login-btn">
                <el-button
                  type="primary"
                  class="submit"
                  size="default"
                  :disabled="loginTypes.includes(2) && loginForm.loginType === 2"
                  @click.native.prevent="handleLogin"
                >
                  登录
                </el-button>
                <div
                  v-if="uiInfo && uiInfo['ui.demo.tips'] && uiInfo['ui.demo.tips'].paramValue"
                  class="demo-tips"
                >
                  {{ uiInfo['ui.demo.tips'].paramValue }}
                </div>
              </div>

              <div
                class="login-msg"
              >
                {{ msg }}
              </div>
            </el-form>

            <!-- 二维码登录区域 -->
            <div
              v-show="codeShow"
              class="code"
            >
              <el-row
                class="code-contaniner"
                :class="isPad && 'is-pad'"
              >
                <plugin-com
                  v-if="codeShow && loginTypes.includes(4) && codeIndex === 4"
                  ref="WecomQr"
                  component-name="WecomQr"
                />
                <plugin-com
                  v-if="codeShow && loginTypes.includes(5) && codeIndex === 5"
                  ref="DingtalkQr"
                  component-name="DingtalkQr"
                />
                <plugin-com
                  v-if="codeShow && loginTypes.includes(6) && codeIndex === 6"
                  ref="LarkQr"
                  component-name="LarkQr"
                />
              </el-row>

              <div
                v-if="qrTypes.length > 1"
                class="login-third-items"
              >
                <span
                  v-if="qrTypes.includes(4)"
                  class="login-third-item login-third-wecom"
                  @click="switchCodeIndex(4)"
                />
                <span
                  v-if="qrTypes.includes(5)"
                  class="login-third-item login-third-dingtalk"
                  @click="switchCodeIndex(5)"
                />
                <span
                  v-if="qrTypes.includes(6)"
                  class="login-third-item login-third-lark"
                  @click="switchCodeIndex(6)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- SSO 和 Lark 组件 -->
      <plugin-com
        v-if="loginTypes.includes(2) && loginForm.loginType === 2"
        ref="SSOComponent"
        component-name="SSOComponent"
      />

      <plugin-com
        v-if="loginTypes.includes(7) && loginForm.loginType === 7"
        ref="LarksuiteQr"
        component-name="LarksuiteQr"
      />
    </div>

    <div
      v-if="showFoot"
      class="dynamic-login-foot"
      v-html="footContent"
    />
  </div>
</template>

<script>

import { encrypt } from '@/utils/rsaEncrypt'
import { ldapStatus, oidcStatus, getPublicKey, pluginLoaded, defaultLoginType, wecomStatus, dingtalkStatus, larkStatus, larksuiteStatus, casStatus, casLoginPage } from '@/api/user'
import { getSysUI } from '@/utils/auth'
import { changeFavicon, showMultiLoginMsg } from '@/utils/index'
import { initTheme } from '@/utils/ThemeUtil'
import PluginCom from '@/views/system/plugin/PluginCom'
import Cookies from 'js-cookie'
import xss from 'xss'
export default {
  name: 'Login',
  components: { PluginCom },
  data() {
    return {
      loginForm: {
        loginType: 0,
        username: '',
        password: ''
      },
      isPad: false,
      loginRules: {
        username: [{ required: true, trigger: 'blur', message: this.$t('commons.input_id') }],
        password: [{ required: true, trigger: 'blur', message: this.$t('commons.input_pwd') }]
      },
      loading: false,
      passwordType: 'password',
      redirect: undefined,
      uiInfo: null,
      loginImageUrl: null,
      loginLogoUrl: null,
      axiosFinished: false,
      loginTypes: [0],
      isPluginLoaded: false,
      contentShow: false,
      clearLocalStorage: [
        'panel-main-tree',
        'panel-default-tree',
        'chart-tree',
        'dataset-tree'
      ],
      defaultType: 0,
      showFoot: false,
      footContent: '',
      codeShow: false,
      imgAppShow: true,
      codeIndex: 4
    }
  },
  computed: {
    msg() {
      return this.$store.state.user.loginMsg
    },
    qrTypes() {
      return this.loginTypes && this.loginTypes.filter(item => item > 3 && item < 7) || []
    },
    radioTypes() {
      return this.loginTypes && this.loginTypes.filter(item => item < 4 || item > 6) || []
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  beforeCreate() {
    pluginLoaded().then(res => {
      this.isPluginLoaded = res.success && res.data
      this.isPluginLoaded && initTheme()
      this.contentShow = true
    }).catch(() => {
      this.contentShow = true
    })

    casStatus().then(res => {
      if (res.success && res.data) {
        this.loginTypes.push(3)
      }
    })

    ldapStatus().then(res => {
      if (res.success && res.data) {
        this.loginTypes.push(1)
      }
      this.setDefaultType()
    })

    oidcStatus().then(res => {
      if (res.success && res.data) {
        this.loginTypes.push(2)
      }
      this.setDefaultType()
    })

    wecomStatus().then(res => {
      if (res.success && res.data) {
        this.loginTypes.push(4)
        const arr = this.loginTypes.filter(item => item > 3)
        this.codeIndex = arr && arr.length && Math.min(...arr) || this.codeIndex
      }
      this.setDefaultType()
    })

    dingtalkStatus().then(res => {
      if (res.success && res.data) {
        this.loginTypes.push(5)
        const arr = this.loginTypes.filter(item => item > 3)
        this.codeIndex = arr && arr.length && Math.min(...arr) || this.codeIndex
      }
      this.setDefaultType()
    })

    larkStatus().then(res => {
      if (res.success && res.data) {
        this.loginTypes.push(6)
        const arr = this.loginTypes.filter(item => item > 3)
        this.codeIndex = arr && arr.length && Math.min(...arr) || this.codeIndex
      }
      this.setDefaultType()
    })

    larksuiteStatus().then(res => {
      if (res.success && res.data) {
        this.loginTypes.push(7)
      }
      this.setDefaultType()
    })

    getPublicKey().then(res => {
      if (res.success && res.data) {
        // 保存公钥
        localStorage.setItem('publicKey', res.data)
      }
    })
    defaultLoginType().then(res => {
      if (res && res.success) {
        this.defaultType = res.data
      }
      if (this.loginTypes.includes(3) && this.defaultType === 3) {
        casLoginPage().then(res => {
          window.location.href = res.data
        })
      }
      this.setDefaultType()
    })
  },

  mounted() {
    this.isPad = /iPad/.test(navigator.userAgent)
  },

  created() {
    this.$store.dispatch('user/getUI').then((res) => {
      this.axiosFinished = true
      this.showLoginImage(res)
    }).catch(err => {
      console.error(err)
    })
    let msg = Cookies.get('OidcError')
    if (msg) {
      msg = msg.replace('+', '')
      this.$error(msg)
    }
    this.clearOidcMsg()

    if (Cookies.get('WecomError')) {
      this.$error(Cookies.get('WecomError'))
      this.switchCodeIndex(4)
    }
    this.clearWecomMsg()

    if (Cookies.get('DingtalkError')) {
      this.$error(Cookies.get('DingtalkError'))
      this.switchCodeIndex(5)
    }
    this.clearDingtalkMsg()

    if (Cookies.get('LarkError')) {
      this.$error(Cookies.get('LarkError'))
      this.switchCodeIndex(6)
    }
    this.clearLarkMsg()

    if (Cookies.get('LarksuiteError')) {
      this.$error(Cookies.get('LarksuiteError'))
    }
    this.clearLarksuiteMsg()
    showMultiLoginMsg()
  },

  methods: {
    switchCodeIndex(codeIndex) {
      this.codeIndex = codeIndex
    },
    showQr() {
      this.codeShow = !this.codeShow
    },
    setDefaultType() {
      if (this.loginTypes.includes(this.defaultType)) {
        this.loginForm.loginType = this.defaultType
        this.$nextTick(() => {
          this.changeLoginType(this.loginForm.loginType)
        })
      }
    },
    clearOidcMsg() {
      Cookies.remove('OidcError')
      Cookies.remove('IdToken')
    },
    clearWecomMsg() {
      Cookies.remove('WecomError')
    },
    clearDingtalkMsg() {
      Cookies.remove('DingtalkError')
    },
    clearLarkMsg() {
      Cookies.remove('LarkError')
    },
    clearLarksuiteMsg() {
      Cookies.remove('LarksuiteError')
    },
    showLoginImage(uiInfo) {
      this.uiInfo = getSysUI()
      if (!this.uiInfo || Object.keys(this.uiInfo).length === 0) {
        this.uiInfo = uiInfo
      }
      if (this.uiInfo['ui.loginImage'] && this.uiInfo['ui.loginImage'].paramValue) {
        this.loginImageUrl = '/system/ui/image/' + this.uiInfo['ui.loginImage'].paramValue
      }
      if (this.uiInfo['ui.loginLogo'] && this.uiInfo['ui.loginLogo'].paramValue) {
        this.loginLogoUrl = '/system/ui/image/' + this.uiInfo['ui.loginLogo'].paramValue
      }

      if (this.uiInfo['ui.favicon'] && this.uiInfo['ui.favicon'].paramValue) {
        const faviconUrl = '/system/ui/image/' + this.uiInfo['ui.favicon'].paramValue
        changeFavicon(faviconUrl)
      }
      if (this.uiInfo['ui.showFoot'] && this.uiInfo['ui.showFoot'].paramValue) {
        this.showFoot = this.uiInfo['ui.showFoot'].paramValue === true || this.uiInfo['ui.showFoot'].paramValue === 'true'
        if (this.showFoot) {
          const content = this.uiInfo['ui.footContent'] && this.uiInfo['ui.footContent'].paramValue
          const myXss = new xss.FilterXSS({
            css: {
              whiteList: {
                'background-color': true,
                'text-align': true,
                'color': true,
                'margin-top': true,
                'margin-bottom': true,
                'line-height': true,
                'box-sizing': true,
                'padding-top': true,
                'padding-bottom': true
              }
            },
            whiteList: {
              ...xss.whiteList,
              p: ['style'],
              span: ['style']
            }
          })
          this.footContent = myXss.process(content)
        }
      }
    },
    initCache() {
      this.clearLocalStorage.forEach(item => {
        localStorage.removeItem(item)
      })
    },

    handleLogin() {
      this.initCache()
      this.clearOidcMsg()
      this.clearWecomMsg()
      this.clearDingtalkMsg()
      this.clearLarkMsg()
      this.clearLarksuiteMsg()
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          const user = {
            username: encrypt(this.loginForm.username),
            password: encrypt(this.loginForm.password),
            loginType: this.loginForm.loginType
          }
          this.$store.dispatch('user/login', user).then(() => {
            this.$router.push({ path: this.redirect || '/' })
            this.loading = false
          }).catch((e) => {
            this.loading = false
            e?.response?.data?.message?.startsWith('MultiLoginError') && this.showMessage()
          })
        } else {
          return false
        }
      })
    },
    showMessage() {
      showMultiLoginMsg()
    },
    changeLoginType(val) {
      if (val !== 2 && val !== 7) return
      this.clearOidcMsg()
      this.clearLarksuiteMsg()
      this.$nextTick(() => {

      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../styles/variables";

@mixin login-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

// 主页面布局
.login-page {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

// 左侧装饰区域
.login-decoration-section {
  flex: 1;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #90caf9 100%);
  position: relative;
  display: flex;
  flex-direction: column;

  .brand-logo {
    position: absolute;
    top: 30px;
    left: 30px;
    z-index: 10;

    .brand-text {
      font-size: 18px;
      font-weight: 600;
      color: #1976d2;
      display: flex;
      align-items: center;

      &::before {
        content: "🔥";
        margin-right: 8px;
        font-size: 20px;
      }
    }
  }

  .decoration-container {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    .decoration-image {
      position: absolute;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;

      &.decoration-1 {
        background-image: url('../../assets/decoration1.png');
        width: 400px;
        height: 400px;
        z-index: 2;
        transform: translate(-50px, -20px);
      }

      &.decoration-2 {
        background-image: url('../../assets/decoration2.png');
        width: 300px;
        height: 300px;
        z-index: 1;
        transform: translate(80px, 50px);
      }
    }
  }
}

// 右侧登录区域
.login-form-section {
  width: 480px;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .login-container {
    width: 100%;
    max-width: 400px;
    padding: 40px;

    .login-content {
      position: relative;
    }
  }
}

// 登录表单样式
.login-form {
  .form-title {
    text-align: center;
    margin-bottom: 40px;

    h2 {
      font-size: 28px;
      font-weight: 600;
      color: #1a1a1a;
      margin: 0 0 8px 0;
    }

    .form-subtitle {
      font-size: 14px;
      color: #666;
      margin: 0;
    }
  }

  .form-content {
    .form-label {
      display: block;
      font-size: 14px;
      color: #333;
      margin-bottom: 8px;
      font-weight: 500;
    }

    ::v-deep .el-form-item {
      margin-bottom: 24px;

      .el-input__inner {
        height: 48px;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        background: #f8f9fa;
        font-size: 14px;
        padding: 0 16px;

        &:focus {
          border-color: $--color-primary;
          background: #ffffff;
          box-shadow: 0 0 0 2px rgba(51, 112, 255, 0.1);
        }

        &::placeholder {
          color: #999;
        }
      }

      .el-radio-group {
        margin-bottom: 16px;

        .el-radio {
          margin-right: 20px;

          .el-radio__label {
            font-size: 14px;
            color: #666;
          }
        }
      }
    }
  }

  .login-btn {
    margin-top: 32px;

    .submit {
      width: 100%;
      height: 48px;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 500;
      background: $--color-primary;
      border: none;

      &:hover {
        background: darken($--color-primary, 10%);
      }
    }

    .demo-tips {
      margin-top: 16px;
      font-size: 14px;
      color: $--color-danger;
      text-align: center;
      line-height: 1.4;
    }
  }

  .login-msg {
    margin-top: 16px;
    color: $--color-danger;
    text-align: center;
    font-size: 14px;
  }
}
// 二维码切换按钮
.trans {
  position: absolute;
  right: 20px;
  top: 20px;
  width: 40px;
  height: 40px;
  background: #f0f0f0 url(../../assets/qrcode.png) no-repeat center/24px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: #e0e0e0;
  }
}

.trans-pc {
  position: absolute;
  right: 20px;
  top: 20px;
  width: 40px;
  height: 40px;
  background: $--color-primary url(../../assets/xianshiqi-2.png) no-repeat center/24px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: darken($--color-primary, 10%);
  }
}

.imgApp {
  width: 40px;
  height: 40px;
  background: linear-gradient(225deg, transparent 20px, #fff 0);
}

// 二维码登录区域
.code {
  width: 100%;
  text-align: center;
  padding: 40px 0;

  img {
    width: 200px;
    height: 200px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .code-contaniner {
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;

    &.is-pad {
      min-height: 250px;
    }
  }

  .login-third-items {
    margin-top: 30px;
    display: flex;
    justify-content: center;
    gap: 16px;
  }
}

.login-third-item {
  display: inline-block;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

.login-third-wecom {
  background: url(../../assets/wecom.png) no-repeat center/cover;
}

.login-third-dingtalk {
  background: url(../../assets/dingding01.png) no-repeat center/cover;
}

.login-third-lark {
  background: url(../../assets/lark.png) no-repeat center/cover;
}

// 页脚
.dynamic-login-foot {
  visibility: visible;
  width: 100%;
  position: fixed;
  z-index: 302;
  bottom: 0;
  left: 0;
  height: auto;
  padding-top: 1px;
  zoom: 1;
  margin: 0;
}

// 响应式设计
@media only screen and (max-width: 1024px) {
  .login-page {
    flex-direction: column;
  }

  .login-decoration-section {
    height: 200px;

    .decoration-container {
      .decoration-image {
        &.decoration-1 {
          width: 200px;
          height: 200px;
          transform: translate(-20px, -10px);
        }

        &.decoration-2 {
          width: 150px;
          height: 150px;
          transform: translate(40px, 20px);
        }
      }
    }
  }

  .login-form-section {
    width: 100%;
    flex: 1;

    .login-container {
      padding: 20px;
    }
  }
}

@media only screen and (max-width: 768px) {
  .login-decoration-section {
    height: 150px;

    .brand-logo {
      top: 15px;
      left: 15px;

      .brand-text {
        font-size: 16px;
      }
    }

    .decoration-container {
      .decoration-image {
        &.decoration-1 {
          width: 120px;
          height: 120px;
          transform: translate(-10px, -5px);
        }

        &.decoration-2 {
          width: 90px;
          height: 90px;
          transform: translate(20px, 10px);
        }
      }
    }
  }

  .login-form-section {
    .login-container {
      padding: 15px;
      max-width: 320px;
    }
  }

  .login-form {
    .form-title {
      margin-bottom: 30px;

      h2 {
        font-size: 24px;
      }
    }

    .form-content {
      ::v-deep .el-form-item {
        margin-bottom: 20px;

        .el-input__inner {
          height: 44px;
        }
      }
    }

    .login-btn {
      margin-top: 24px;

      .submit {
        height: 44px;
        font-size: 15px;
      }
    }
  }
}

</style>
